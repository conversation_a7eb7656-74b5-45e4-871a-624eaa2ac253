"""
量化交易训练数据生成脚本
按标的分别生成训练/验证/测试数据文件
"""
import pandas as pd
import numpy as np
import os
from typing import Tuple, List, Dict
from sklearn.preprocessing import StandardScaler
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from quant.tdx_data import get_all_symbols, get_data


class QuantDataGenerator:
    """量化数据生成器 - 按标的分别生成数据文件"""

    def __init__(self, lookback_days: int = 60, predict_days: int = 5, test_years: int = 2):
        """
        初始化数据生成器

        Args:
            lookback_days: 历史数据天数
            predict_days: 预测天数
            test_years: 最后几年用于验证/测试
        """
        self.lookback_days = lookback_days
        self.predict_days = predict_days
        self.test_years = test_years
        self.base_date = datetime(1990, 1, 1)  # 基准日期

        # 创建输出目录
        os.makedirs('data/train', exist_ok=True)
        os.makedirs('data/val', exist_ok=True)
        os.makedirs('data/test', exist_ok=True)
        
    def filter_symbols(self) -> pd.DataFrame:
        """筛选符合条件的证券"""
        all_symbols = get_all_symbols()
        
        # 限制证券类型
        allowed_types = ['SH_A_STOCK', 'SZ_A_STOCK', 'SH_INDEX', 'SZ_INDEX', 'SH_FUND', 'SZ_FUND']
        filtered_symbols = all_symbols[all_symbols['security_type'].isin(allowed_types)]
        
        print(f"筛选后的证券数量: {len(filtered_symbols)}")
        return filtered_symbols
    
    def add_date_features(self, df: pd.DataFrame, code: str) -> pd.DataFrame:
        """添加日期相关特征"""
        data = df.copy()

        # 转换日期格式并计算日期数
        data['date'] = pd.to_datetime(data['datetime'].astype(str))
        data['date_num'] = (data['date'] - self.base_date).dt.days

        # 生成ID列
        data['id'] = data.apply(lambda row: f"{code}_{row['date_num']}", axis=1)

        return data

    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标特征，保留OHLCVA基础特征"""
        data = df.copy()

        # 价格相关特征
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))

        # 移动平均
        for window in [5, 10, 20, 30]:
            data[f'ma_{window}'] = data['close'].rolling(window).mean()
            data[f'ma_ratio_{window}'] = data['close'] / data[f'ma_{window}']

        # 技术指标
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))

        # 布林带
        data['bb_middle'] = data['close'].rolling(20).mean()
        bb_std = data['close'].rolling(20).std()
        data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
        data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])

        # 成交量特征
        data['volume_ma'] = data['volume'].rolling(20).mean()
        data['volume_ratio'] = data['volume'] / data['volume_ma']
        data['amount_ratio'] = data['amount'].pct_change()

        # 波动率特征
        data['volatility'] = data['returns'].rolling(20).std()
        data['high_low_ratio'] = (data['high'] - data['low']) / data['close']

        # ATR (Average True Range)
        data['tr1'] = data['high'] - data['low']
        data['tr2'] = abs(data['high'] - data['close'].shift(1))
        data['tr3'] = abs(data['low'] - data['close'].shift(1))
        data['tr'] = data[['tr1', 'tr2', 'tr3']].max(axis=1)
        data['atr'] = data['tr'].rolling(14).mean()
        data['atr_ratio'] = data['atr'] / data['close']

        return data
    
    def calculate_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算预测标签"""
        data = df.copy()
        
        # 未来5日收益率
        data['future_return'] = (data['close'].shift(-self.predict_days) / data['close'] - 1)
        
        # 效率比率 (Efficiency Ratio)
        future_close = data['close'].shift(-self.predict_days)
        net_change = abs(future_close - data['close'])
        # 计算未来5天的总价格变动
        total_change = pd.Series(0.0, index=data.index)
        for i in range(1, self.predict_days + 1):
            total_change += abs(data['close'].shift(-i) - data['close'].shift(-i+1))
        data['efficiency_ratio'] = net_change / (total_change + 1e-8)
        
        # 未来5日最大上涨
        # 计算未来5天内的最高价
        future_highs = []
        for i in range(1, self.predict_days + 1):
            future_highs.append(data['high'].shift(-i))
        future_high_df = pd.concat(future_highs, axis=1)
        max_future_high = future_high_df.max(axis=1)
        data['max_upside'] = (max_future_high / data['close'] - 1)
        
        # 未来5日最大回撤
        future_prices = []
        for i in range(1, self.predict_days + 1):
            future_prices.append(data['close'].shift(-i))
        future_df = pd.concat(future_prices, axis=1)
        running_max = future_df.expanding(axis=1).max()
        drawdowns = (future_df - running_max) / running_max
        data['max_drawdown'] = drawdowns.min(axis=1)
        
        return data
    
    def filter_inactive_stocks(self, df: pd.DataFrame, min_price_change: float = 0.05) -> bool:
        """过滤价格基本无变动的标的"""
        if len(df) < self.lookback_days:
            return False
            
        recent_data = df.tail(self.lookback_days)
        price_std = recent_data['close'].std()
        price_mean = recent_data['close'].mean()
        cv = price_std / price_mean  # 变异系数
        
        return cv > min_price_change
    
    def prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """准备特征数据，包含OHLCVA基础特征和衍生特征"""
        feature_cols = [
            # OHLCVA基础特征
            'open', 'high', 'low', 'close', 'volume', 'amount',
            # 日期特征
            'date_num',
            # 衍生技术特征
            'returns', 'log_returns', 'ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20', 'ma_ratio_30',
            'rsi', 'bb_position', 'volume_ratio', 'amount_ratio', 'volatility',
            'high_low_ratio', 'atr_ratio'
        ]

        features = df[feature_cols].values
        return features
    
    def create_sequences_with_indices(self, features: np.ndarray, labels: np.ndarray, ids: List[str]) -> Tuple[List[Tuple], List[str], List[np.ndarray]]:
        """创建时间序列数据，返回切片索引、ID和标签"""
        slice_indices = []  # 存储特征切片的起始和结束索引
        sequence_ids = []   # 存储对应的ID
        sequence_labels = [] # 存储对应的标签

        for i in range(self.lookback_days, len(features) - self.predict_days):
            # 存储切片索引而不是实际数据
            slice_indices.append((i - self.lookback_days, i))
            # 使用最近一天（第i-1天）的ID和标签
            sequence_ids.append(ids[i-1])
            sequence_labels.append(labels[i-1])

        return slice_indices, sequence_ids, sequence_labels
    
    def split_data_by_time(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """按时间分割数据：训练/验证/测试"""
        df_sorted = df.sort_values('date')

        # 最后2年用于验证和测试
        cutoff_date = df_sorted['date'].max() - pd.DateOffset(years=self.test_years)
        val_test_cutoff = df_sorted['date'].max() - pd.DateOffset(years=1)  # 最后1年测试，倒数第2年验证

        train_df = df_sorted[df_sorted['date'] < cutoff_date]
        val_df = df_sorted[(df_sorted['date'] >= cutoff_date) & (df_sorted['date'] < val_test_cutoff)]
        test_df = df_sorted[df_sorted['date'] >= val_test_cutoff]

        return train_df, val_df, test_df

    def calculate_normalization_stats(self, train_features: np.ndarray) -> Dict:
        """基于训练数据计算标准化统计量"""
        scaler = StandardScaler()
        scaler.fit(train_features.reshape(-1, train_features.shape[-1]))

        return {
            'mean': scaler.mean_,
            'scale': scaler.scale_,
            'var': scaler.var_
        }

    def process_single_symbol(self, symbol_info: pd.Series) -> bool:
        """处理单个标的数据"""
        try:
            code = symbol_info['code']
            print(f"处理标的: {code}")

            # 获取数据
            df = get_data(code, fq=2)

            if df is None or len(df) < self.lookback_days + self.predict_days + 365 * self.test_years:
                print(f"  数据不足，跳过")
                return False

            # 过滤不活跃的股票
            if not self.filter_inactive_stocks(df):
                print(f"  价格变动不足，跳过")
                return False

            # 添加code列用于生成ID
            df['code'] = code

            # 添加日期特征
            df = self.add_date_features(df, code)
            df = df.sort_values('date')

            # 计算技术指标
            df = self.calculate_technical_indicators(df)

            # 计算标签
            df = self.calculate_labels(df)

            # 删除包含NaN的行
            df = df.dropna()

            if len(df) < self.lookback_days + self.predict_days + 365 * self.test_years:
                print(f"  清洗后数据不足，跳过")
                return False

            # 按时间分割数据
            train_df, val_df, test_df = self.split_data_by_time(df)

            if len(train_df) < self.lookback_days or len(val_df) < self.predict_days or len(test_df) < self.predict_days:
                print(f"  分割后数据不足，跳过")
                return False

            # 准备特征和标签
            all_features = self.prepare_features(df)
            all_labels = df[['future_return', 'efficiency_ratio', 'max_upside', 'max_drawdown']].values
            all_ids = df['id'].tolist()

            # 基于训练数据计算标准化统计量
            train_features = self.prepare_features(train_df)
            norm_stats = self.calculate_normalization_stats(train_features)

            # 为每个数据集创建序列
            datasets = {
                'train': (train_df, 'data/train'),
                'val': (val_df, 'data/val'),
                'test': (test_df, 'data/test')
            }

            for split_name, (split_df, output_dir) in datasets.items():
                if len(split_df) < self.lookback_days + self.predict_days:
                    continue

                # 获取该分割的起始和结束索引
                start_idx = df.index.get_loc(split_df.index[0])
                end_idx = df.index.get_loc(split_df.index[-1]) + 1

                # 创建序列（基于全局索引）
                slice_indices, sequence_ids, sequence_labels = [], [], []

                for i in range(start_idx + self.lookback_days, end_idx - self.predict_days):
                    if i < len(all_features):
                        slice_indices.append((i - self.lookback_days, i))
                        # 使用最近一天（第i-1天）的ID和标签
                        sequence_ids.append(all_ids[i-1])
                        sequence_labels.append(all_labels[i-1])

                if len(slice_indices) > 0:
                    # 保存数据文件
                    output_file = os.path.join(output_dir, f"{code}.npz")
                    np.savez_compressed(
                        output_file,
                        slice_indices=np.array(slice_indices),
                        ids=np.array(sequence_ids),
                        labels=np.array(sequence_labels),
                        norm_stats=norm_stats,
                        features_full=all_features
                    )
                    print(f"  {split_name}: {len(slice_indices)} 样本")

            return True

        except Exception as e:
            print(f"处理 {symbol_info['code']} 时出错: {e}")
            return False


    def generate_all_data(self):
        """生成所有标的的数据文件"""
        symbols = self.filter_symbols()

        success_count = 0
        total_count = len(symbols)

        print(f"开始处理 {total_count} 个标的...")

        for _, symbol_info in symbols.iterrows():
            if self.process_single_symbol(symbol_info):
                success_count += 1

        print(f"\n数据生成完成！")
        print(f"成功处理: {success_count}/{total_count} 个标的")
        print(f"数据文件保存在: data/train/, data/val/, data/test/")


if __name__ == "__main__":
    # 生成按标的分类的训练数据
    generator = QuantDataGenerator(lookback_days=60, predict_days=5, test_years=2)
    generator.generate_all_data()

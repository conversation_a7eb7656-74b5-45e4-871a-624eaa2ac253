"""
量化交易训练数据生成脚本
生成用于深度学习模型的特征和标签数据
"""
import pandas as pd
import numpy as np
from typing import Tuple, List
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from quant.tdx_data import get_all_symbols, get_data


class QuantDataGenerator:
    """量化数据生成器"""
    
    def __init__(self, lookback_days: int = 60, predict_days: int = 5):
        """
        初始化数据生成器
        
        Args:
            lookback_days: 历史数据天数
            predict_days: 预测天数
        """
        self.lookback_days = lookback_days
        self.predict_days = predict_days
        self.scaler = StandardScaler()
        
    def filter_symbols(self) -> pd.DataFrame:
        """筛选符合条件的证券"""
        all_symbols = get_all_symbols()
        
        # 限制证券类型
        allowed_types = ['SH_A_STOCK', 'SZ_A_STOCK', 'SH_INDEX', 'SZ_INDEX', 'SH_FUND', 'SZ_FUND']
        filtered_symbols = all_symbols[all_symbols['security_type'].isin(allowed_types)]
        
        print(f"筛选后的证券数量: {len(filtered_symbols)}")
        return filtered_symbols
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标特征"""
        data = df.copy()
        
        # 价格相关特征
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        
        # 移动平均
        for window in [5, 10, 20, 30]:
            data[f'ma_{window}'] = data['close'].rolling(window).mean()
            data[f'ma_ratio_{window}'] = data['close'] / data[f'ma_{window}']
        
        # 技术指标
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        data['bb_middle'] = data['close'].rolling(20).mean()
        bb_std = data['close'].rolling(20).std()
        data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
        data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
        
        # 成交量特征
        data['volume_ma'] = data['volume'].rolling(20).mean()
        data['volume_ratio'] = data['volume'] / data['volume_ma']
        data['amount_ratio'] = data['amount'].pct_change()
        
        # 波动率特征
        data['volatility'] = data['returns'].rolling(20).std()
        data['high_low_ratio'] = (data['high'] - data['low']) / data['close']
        
        # ATR (Average True Range)
        data['tr1'] = data['high'] - data['low']
        data['tr2'] = abs(data['high'] - data['close'].shift(1))
        data['tr3'] = abs(data['low'] - data['close'].shift(1))
        data['tr'] = data[['tr1', 'tr2', 'tr3']].max(axis=1)
        data['atr'] = data['tr'].rolling(14).mean()
        data['atr_ratio'] = data['atr'] / data['close']
        
        return data
    
    def calculate_labels(self, df: pd.DataFrame) -> pd.DataFrame:
        """计算预测标签"""
        data = df.copy()
        
        # 未来5日收益率
        data['future_return'] = (data['close'].shift(-self.predict_days) / data['close'] - 1)
        
        # 效率比率 (Efficiency Ratio)
        future_close = data['close'].shift(-self.predict_days)
        net_change = abs(future_close - data['close'])
        total_change = 0
        for i in range(1, self.predict_days + 1):
            total_change += abs(data['close'].shift(-i) - data['close'].shift(-i+1))
        data['efficiency_ratio'] = net_change / (total_change + 1e-8)
        
        # 未来5日最大上涨
        max_high = data['high'].rolling(self.predict_days).max().shift(-self.predict_days)
        data['max_upside'] = (max_high / data['close'] - 1)
        
        # 未来5日最大回撤
        future_prices = []
        for i in range(1, self.predict_days + 1):
            future_prices.append(data['close'].shift(-i))
        future_df = pd.concat(future_prices, axis=1)
        running_max = future_df.expanding(axis=1).max()
        drawdowns = (future_df - running_max) / running_max
        data['max_drawdown'] = drawdowns.min(axis=1)
        
        return data
    
    def filter_inactive_stocks(self, df: pd.DataFrame, min_price_change: float = 0.05) -> bool:
        """过滤价格基本无变动的标的"""
        if len(df) < self.lookback_days:
            return False
            
        recent_data = df.tail(self.lookback_days)
        price_std = recent_data['close'].std()
        price_mean = recent_data['close'].mean()
        cv = price_std / price_mean  # 变异系数
        
        return cv > min_price_change
    
    def prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """准备特征数据"""
        feature_cols = [
            'returns', 'log_returns', 'ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20', 'ma_ratio_30',
            'rsi', 'bb_position', 'volume_ratio', 'amount_ratio', 'volatility', 
            'high_low_ratio', 'atr_ratio'
        ]
        
        features = df[feature_cols].values
        return features
    
    def create_sequences(self, features: np.ndarray, labels: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """创建时间序列数据"""
        X, y = [], []
        
        for i in range(self.lookback_days, len(features) - self.predict_days):
            X.append(features[i-self.lookback_days:i])
            y.append(labels[i])
            
        return np.array(X), np.array(y)
    
    def generate_training_data(self, test_years: int = 2) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """生成训练数据"""
        symbols = self.filter_symbols()
        
        all_X, all_y = [], []
        
        for _, symbol_info in symbols.iterrows():
            try:
                # 获取数据
                df = get_data(symbol_info['code'], fq=2)
                
                if df is None or len(df) < self.lookback_days + self.predict_days + 365 * test_years:
                    continue
                
                # 转换日期格式
                df['date'] = pd.to_datetime(df['datetime'].astype(str))
                df = df.sort_values('date')
                
                # 只使用最近指定年份的数据
                cutoff_date = df['date'].max() - pd.DateOffset(years=test_years)
                df = df[df['date'] >= cutoff_date]
                
                # 过滤不活跃的股票
                if not self.filter_inactive_stocks(df):
                    continue
                
                # 计算技术指标
                df = self.calculate_technical_indicators(df)
                
                # 计算标签
                df = self.calculate_labels(df)
                
                # 删除包含NaN的行
                df = df.dropna()
                
                if len(df) < self.lookback_days + self.predict_days:
                    continue
                
                # 准备特征和标签
                features = self.prepare_features(df)
                labels = df[['future_return', 'efficiency_ratio', 'max_upside', 'max_drawdown']].values
                
                # 创建序列
                X, y = self.create_sequences(features, labels)
                
                if len(X) > 0:
                    all_X.append(X)
                    all_y.append(y)
                    
            except Exception as e:
                print(f"处理 {symbol_info['code']} 时出错: {e}")
                continue
        
        if not all_X:
            raise ValueError("没有生成任何有效数据")
        
        # 合并所有数据
        X_all = np.vstack(all_X)
        y_all = np.vstack(all_y)
        
        # 标准化特征
        X_reshaped = X_all.reshape(-1, X_all.shape[-1])
        X_scaled = self.scaler.fit_transform(X_reshaped)
        X_all = X_scaled.reshape(X_all.shape)
        
        # 分割数据：一半验证，一半测试
        split_idx = len(X_all) // 2
        
        X_val, X_test = X_all[:split_idx], X_all[split_idx:]
        y_val, y_test = y_all[:split_idx], y_all[split_idx:]
        
        print(f"验证集大小: {X_val.shape}")
        print(f"测试集大小: {X_test.shape}")
        
        return X_val, y_val, X_test, y_test


if __name__ == "__main__":
    # 生成训练数据
    generator = QuantDataGenerator(lookback_days=60, predict_days=5)
    X_val, y_val, X_test, y_test = generator.generate_training_data(test_years=2)
    
    # 保存数据
    np.save('data/X_val.npy', X_val)
    np.save('data/y_val.npy', y_val)
    np.save('data/X_test.npy', X_test)
    np.save('data/y_test.npy', y_test)
    
    print("训练数据生成完成！")

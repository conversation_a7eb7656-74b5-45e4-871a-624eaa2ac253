"""
量化交易深度学习模型
支持多任务学习和可配置正则化
"""
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
from typing import Tuple, Optional
import warnings
warnings.filterwarnings('ignore')


class QuantModel(nn.Module):
    """量化交易预测模型"""
    
    def __init__(
        self, 
        input_size: int = 13,
        hidden_size: int = 128,
        num_layers: int = 2,
        output_size: int = 4,
        use_regularization: bool = True,
        dropout_rate: float = 0.3,
        l2_reg: float = 1e-4
    ):
        """
        初始化模型
        
        Args:
            input_size: 输入特征维度
            hidden_size: LSTM隐藏层大小
            num_layers: LSTM层数
            output_size: 输出维度 (4个预测目标)
            use_regularization: 是否使用正则化
            dropout_rate: Dropout比率
            l2_reg: L2正则化系数
        """
        super(QuantModel, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.output_size = output_size
        self.use_regularization = use_regularization
        self.l2_reg = l2_reg
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_rate if use_regularization and num_layers > 1 else 0
        )
        
        # 全连接层
        self.fc_layers = nn.ModuleList()
        
        # 第一个全连接层
        self.fc_layers.append(nn.Linear(hidden_size, hidden_size // 2))
        
        # 可选的Dropout层
        if use_regularization:
            self.dropout1 = nn.Dropout(dropout_rate)
        
        # 第二个全连接层
        self.fc_layers.append(nn.Linear(hidden_size // 2, hidden_size // 4))
        
        # 可选的Dropout层
        if use_regularization:
            self.dropout2 = nn.Dropout(dropout_rate)
        
        # 输出层 - 多任务输出
        self.output_layers = nn.ModuleDict({
            'return_head': nn.Linear(hidden_size // 4, 1),      # 收益率预测
            'efficiency_head': nn.Linear(hidden_size // 4, 1),  # 效率比率预测
            'upside_head': nn.Linear(hidden_size // 4, 1),      # 最大上涨预测
            'drawdown_head': nn.Linear(hidden_size // 4, 1)     # 最大回撤预测
        })
        
        # 激活函数
        self.relu = nn.ReLU()
        self.tanh = nn.Tanh()
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化模型权重"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'lstm' in name:
                    nn.init.xavier_uniform_(param)
                else:
                    nn.init.kaiming_normal_(param, mode='fan_out', nonlinearity='relu')
            elif 'bias' in name:
                nn.init.constant_(param, 0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, seq_len, input_size]
            
        Returns:
            输出张量 [batch_size, output_size]
        """
        batch_size = x.size(0)
        
        # LSTM层
        lstm_out, (hidden, cell) = self.lstm(x)
        
        # 使用最后一个时间步的输出
        last_output = lstm_out[:, -1, :]  # [batch_size, hidden_size]
        
        # 第一个全连接层
        x = self.fc_layers[0](last_output)
        x = self.relu(x)
        
        # 可选的Dropout
        if self.use_regularization:
            x = self.dropout1(x)
        
        # 第二个全连接层
        x = self.fc_layers[1](x)
        x = self.relu(x)
        
        # 可选的Dropout
        if self.use_regularization:
            x = self.dropout2(x)
        
        # 多任务输出
        outputs = []
        outputs.append(self.output_layers['return_head'](x))      # 收益率
        outputs.append(self.output_layers['efficiency_head'](x))  # 效率比率
        outputs.append(self.output_layers['upside_head'](x))      # 最大上涨
        outputs.append(self.tanh(self.output_layers['drawdown_head'](x)))  # 最大回撤 (限制在-1到1)
        
        return torch.cat(outputs, dim=1)  # [batch_size, 4]
    
    def get_l2_loss(self) -> torch.Tensor:
        """计算L2正则化损失"""
        if not self.use_regularization:
            return torch.tensor(0.0)
        
        l2_loss = torch.tensor(0.0)
        for param in self.parameters():
            l2_loss += torch.norm(param, 2) ** 2
        
        return self.l2_reg * l2_loss


class QuantTrainer:
    """模型训练器"""
    
    def __init__(
        self,
        model: QuantModel,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu',
        learning_rate: float = 0.001,
        weight_decay: float = 1e-4
    ):
        """
        初始化训练器
        
        Args:
            model: 模型实例
            device: 训练设备
            learning_rate: 学习率
            weight_decay: 权重衰减
        """
        self.model = model.to(device)
        self.device = device
        
        # 优化器 - 如果模型使用正则化，则不在优化器中使用weight_decay
        if model.use_regularization:
            self.optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        else:
            self.optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
        
        # 损失函数 - 使用Huber Loss，对异常值更鲁棒
        self.criterion = nn.SmoothL1Loss()
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='min', factor=0.5, patience=10, verbose=True
        )
    
    def train_epoch(self, train_loader: DataLoader) -> float:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        
        for batch_x, batch_y in train_loader:
            batch_x = batch_x.to(self.device)
            batch_y = batch_y.to(self.device)
            
            # 前向传播
            outputs = self.model(batch_x)
            
            # 计算损失
            loss = self.criterion(outputs, batch_y)
            
            # 添加L2正则化损失
            if self.model.use_regularization:
                loss += self.model.get_l2_loss()
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            total_loss += loss.item()
        
        return total_loss / len(train_loader)
    
    def validate(self, val_loader: DataLoader) -> float:
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x = batch_x.to(self.device)
                batch_y = batch_y.to(self.device)
                
                outputs = self.model(batch_x)
                loss = self.criterion(outputs, batch_y)
                
                if self.model.use_regularization:
                    loss += self.model.get_l2_loss()
                
                total_loss += loss.item()
        
        return total_loss / len(val_loader)
    
    def train(
        self, 
        train_loader: DataLoader, 
        val_loader: DataLoader, 
        epochs: int = 100,
        early_stopping_patience: int = 20
    ) -> Tuple[list, list]:
        """训练模型"""
        train_losses = []
        val_losses = []
        best_val_loss = float('inf')
        patience_counter = 0
        
        print(f"开始训练，使用设备: {self.device}")
        print(f"正则化设置: {self.model.use_regularization}")
        
        for epoch in range(epochs):
            # 训练
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss = self.validate(val_loader)
            
            train_losses.append(train_loss)
            val_losses.append(val_loss)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # 保存最佳模型
                torch.save(self.model.state_dict(), 'best_model.pth')
            else:
                patience_counter += 1
            
            if epoch % 10 == 0:
                print(f'Epoch {epoch:3d}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')
            
            if patience_counter >= early_stopping_patience:
                print(f'Early stopping at epoch {epoch}')
                break
        
        # 加载最佳模型
        self.model.load_state_dict(torch.load('best_model.pth'))
        
        return train_losses, val_losses


def create_data_loaders(
    X_train: np.ndarray, 
    y_train: np.ndarray, 
    X_val: np.ndarray, 
    y_val: np.ndarray,
    batch_size: int = 64
) -> Tuple[DataLoader, DataLoader]:
    """创建数据加载器"""
    
    # 转换为PyTorch张量
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    X_val_tensor = torch.FloatTensor(X_val)
    y_val_tensor = torch.FloatTensor(y_val)
    
    # 创建数据集
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, val_loader


if __name__ == "__main__":
    # 加载数据
    X_val = np.load('data/X_val.npy')
    y_val = np.load('data/y_val.npy')
    X_test = np.load('data/X_test.npy')
    y_test = np.load('data/y_test.npy')
    
    print(f"数据形状: X_val: {X_val.shape}, y_val: {y_val.shape}")
    
    # 创建数据加载器
    train_loader, val_loader = create_data_loaders(X_val, y_val, X_test, y_test)
    
    # 创建模型 - 使用正则化
    model_with_reg = QuantModel(
        input_size=X_val.shape[-1],
        hidden_size=128,
        num_layers=2,
        output_size=4,
        use_regularization=True
    )
    
    # 训练器
    trainer = QuantTrainer(model_with_reg, learning_rate=0.001)
    
    # 训练模型
    train_losses, val_losses = trainer.train(train_loader, val_loader, epochs=100)
    
    print("模型训练完成！")

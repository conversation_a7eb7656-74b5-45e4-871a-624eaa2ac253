"""
测试数据生成逻辑的正确性
验证标签计算、特征衍生和对应关系
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 模拟数据生成器的部分功能进行测试
class TestDataGenerator:
    def __init__(self, lookback_days=60, predict_days=5):
        self.lookback_days = lookback_days
        self.predict_days = predict_days
        self.base_date = datetime(1990, 1, 1)
    
    def create_test_data(self, n_days=100):
        """创建测试用的模拟股票数据"""
        dates = [datetime(2023, 1, 1) + timedelta(days=i) for i in range(n_days)]
        
        # 模拟价格数据（随机游走）
        np.random.seed(42)
        base_price = 100
        returns = np.random.normal(0, 0.02, n_days)
        prices = [base_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # 创建OHLCV数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            high = close * (1 + abs(np.random.normal(0, 0.01)))
            low = close * (1 - abs(np.random.normal(0, 0.01)))
            open_price = close * (1 + np.random.normal(0, 0.005))
            volume = np.random.randint(1000000, 10000000)
            amount = volume * close
            
            data.append({
                'datetime': int(date.strftime('%Y%m%d')),
                'open': open_price,
                'high': high,
                'low': low,
                'close': close,
                'volume': volume,
                'amount': amount
            })
        
        return pd.DataFrame(data)
    
    def add_date_features(self, df, code='test001'):
        """添加日期特征"""
        data = df.copy()
        data['date'] = pd.to_datetime(data['datetime'].astype(str))
        data['date_num'] = (data['date'] - self.base_date).dt.days
        data['id'] = data.apply(lambda row: f"{code}_{row['date_num']}", axis=1)
        return data
    
    def calculate_technical_indicators(self, df):
        """计算技术指标"""
        data = df.copy()
        
        # 基础特征
        data['returns'] = data['close'].pct_change()
        data['log_returns'] = np.log(data['close'] / data['close'].shift(1))
        
        # 移动平均
        for window in [5, 10, 20, 30]:
            data[f'ma_{window}'] = data['close'].rolling(window).mean()
            data[f'ma_ratio_{window}'] = data['close'] / data[f'ma_{window}']
        
        # RSI
        delta = data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        data['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        data['bb_middle'] = data['close'].rolling(20).mean()
        bb_std = data['close'].rolling(20).std()
        data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
        data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])
        
        # 成交量特征
        data['volume_ma'] = data['volume'].rolling(20).mean()
        data['volume_ratio'] = data['volume'] / data['volume_ma']
        data['amount_ratio'] = data['amount'].pct_change()
        
        # 波动率特征
        data['volatility'] = data['returns'].rolling(20).std()
        data['high_low_ratio'] = (data['high'] - data['low']) / data['close']
        
        # ATR
        data['tr1'] = data['high'] - data['low']
        data['tr2'] = abs(data['high'] - data['close'].shift(1))
        data['tr3'] = abs(data['low'] - data['close'].shift(1))
        data['tr'] = data[['tr1', 'tr2', 'tr3']].max(axis=1)
        data['atr'] = data['tr'].rolling(14).mean()
        data['atr_ratio'] = data['atr'] / data['close']
        
        return data
    
    def calculate_labels(self, df):
        """计算预测标签"""
        data = df.copy()
        
        # 未来5日收益率
        data['future_return'] = (data['close'].shift(-self.predict_days) / data['close'] - 1)
        
        # 效率比率
        future_close = data['close'].shift(-self.predict_days)
        net_change = abs(future_close - data['close'])
        total_change = pd.Series(0.0, index=data.index)
        for i in range(1, self.predict_days + 1):
            total_change += abs(data['close'].shift(-i) - data['close'].shift(-i+1))
        data['efficiency_ratio'] = net_change / (total_change + 1e-8)
        
        # 未来5日最大上涨
        future_highs = []
        for i in range(1, self.predict_days + 1):
            future_highs.append(data['high'].shift(-i))
        future_high_df = pd.concat(future_highs, axis=1)
        max_future_high = future_high_df.max(axis=1)
        data['max_upside'] = (max_future_high / data['close'] - 1)
        
        # 未来5日最大回撤
        future_prices = []
        for i in range(1, self.predict_days + 1):
            future_prices.append(data['close'].shift(-i))
        future_df = pd.concat(future_prices, axis=1)
        running_max = future_df.expanding(axis=1).max()
        drawdowns = (future_df - running_max) / running_max
        data['max_drawdown'] = drawdowns.min(axis=1)
        
        return data
    
    def test_label_calculations(self):
        """测试标签计算的正确性"""
        print("=== 测试标签计算逻辑 ===")
        
        # 创建简单的测试数据
        test_data = pd.DataFrame({
            'datetime': [20230101, 20230102, 20230103, 20230104, 20230105, 20230106, 20230107, 20230108],
            'close': [100, 102, 101, 105, 103, 107, 106, 108],
            'high': [101, 103, 102, 106, 104, 108, 107, 109],
            'low': [99, 101, 100, 104, 102, 106, 105, 107],
            'open': [100, 102, 101, 105, 103, 107, 106, 108],
            'volume': [1000000] * 8,
            'amount': [100000000] * 8
        })
        
        # 添加日期特征
        test_data = self.add_date_features(test_data)
        
        # 计算标签
        test_data = self.calculate_labels(test_data)
        
        # 验证第0天的标签（基于第0天预测第1-5天）
        print(f"第0天收盘价: {test_data.loc[0, 'close']}")
        print(f"第5天收盘价: {test_data.loc[5, 'close']}")
        expected_return = (test_data.loc[5, 'close'] / test_data.loc[0, 'close']) - 1
        actual_return = test_data.loc[0, 'future_return']
        print(f"预期收益率: {expected_return:.4f}")
        print(f"计算收益率: {actual_return:.4f}")
        print(f"收益率计算正确: {abs(expected_return - actual_return) < 1e-6}")
        
        # 验证最大上涨
        future_highs = test_data.loc[1:5, 'high'].max()
        expected_upside = (future_highs / test_data.loc[0, 'close']) - 1
        actual_upside = test_data.loc[0, 'max_upside']
        print(f"预期最大上涨: {expected_upside:.4f}")
        print(f"计算最大上涨: {actual_upside:.4f}")
        print(f"最大上涨计算正确: {abs(expected_upside - actual_upside) < 1e-6}")
        
        print()
    
    def test_sequence_correspondence(self):
        """测试序列对应关系"""
        print("=== 测试特征-ID-标签对应关系 ===")
        
        # 创建测试数据
        df = self.create_test_data(80)
        df = self.add_date_features(df)
        df = self.calculate_technical_indicators(df)
        df = self.calculate_labels(df)
        df = df.dropna()
        
        # 模拟序列创建逻辑
        feature_cols = [
            'open', 'high', 'low', 'close', 'volume', 'amount', 'date_num',
            'returns', 'log_returns', 'ma_ratio_5', 'ma_ratio_10', 'ma_ratio_20', 'ma_ratio_30',
            'rsi', 'bb_position', 'volume_ratio', 'amount_ratio', 'volatility', 
            'high_low_ratio', 'atr_ratio'
        ]
        
        features = df[feature_cols].values
        labels = df[['future_return', 'efficiency_ratio', 'max_upside', 'max_drawdown']].values
        ids = df['id'].tolist()
        
        # 测试几个样本的对应关系
        for test_idx in [self.lookback_days, self.lookback_days + 10, self.lookback_days + 20]:
            if test_idx < len(features) - self.predict_days:
                print(f"\n测试索引 {test_idx}:")
                
                # 特征：第 test_idx-60 到 test_idx-1 天
                feature_slice = features[test_idx-self.lookback_days:test_idx]
                print(f"特征时间范围: 第{test_idx-self.lookback_days}天 到 第{test_idx-1}天")
                print(f"特征最后一天收盘价: {feature_slice[-1][3]:.2f}")  # close价格
                
                # ID：第 test_idx-1 天
                sample_id = ids[test_idx-1]
                print(f"样本ID: {sample_id}")
                
                # 标签：第 test_idx-1 天对应的未来标签
                sample_label = labels[test_idx-1]
                print(f"标签 (收益率): {sample_label[0]:.4f}")
                
                # 验证：手动计算收益率
                current_close = df.iloc[test_idx-1]['close']
                future_close = df.iloc[test_idx-1+self.predict_days]['close'] if test_idx-1+self.predict_days < len(df) else None
                if future_close is not None:
                    manual_return = (future_close / current_close) - 1
                    print(f"手动计算收益率: {manual_return:.4f}")
                    print(f"对应关系正确: {abs(manual_return - sample_label[0]) < 1e-6}")
                
        print()


def main():
    """运行所有测试"""
    print("开始测试数据生成逻辑...")
    
    tester = TestDataGenerator()
    
    # 测试标签计算
    tester.test_label_calculations()
    
    # 测试序列对应关系
    tester.test_sequence_correspondence()
    
    print("测试完成！")


if __name__ == "__main__":
    main()
